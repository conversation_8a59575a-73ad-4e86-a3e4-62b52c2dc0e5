{"version": 1, "revision": 0, "rules": [{"primary-output": "C:\\Users\\<USER>\\Desktop\\coding\\nebula\\Nebula loader\\Build\\DEV\\obj\\microsoft\\STL\\std.compat.obj", "outputs": ["std.compat.i", "C:\\Users\\<USER>\\Desktop\\coding\\nebula\\Nebula loader\\Build\\DEV\\obj\\microsoft\\STL\\std.compat.ixx.ifc.dt.module.json", "C:\\Users\\<USER>\\Desktop\\coding\\nebula\\Nebula loader\\Build\\DEV\\obj\\microsoft\\STL\\std.compat.ixx.ifc.dt.d.json", "C:\\Users\\<USER>\\Desktop\\coding\\nebula\\Nebula loader\\Build\\DEV\\obj\\microsoft\\STL\\std.compat.ifc"], "provides": [{"logical-name": "std.compat", "source-path": "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.43.34808\\modules\\std.compat.ixx", "is-interface": true}], "requires": [{"logical-name": "std"}]}]}
@echo off
echo Testing Nebula Loader build configuration...
echo.

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" >nul 2>&1
if errorlevel 1 (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" >nul 2>&1
    if errorlevel 1 (
        echo ERROR: Visual Studio 2022 not found!
        pause
        exit /b 1
    )
)

echo Visual Studio environment loaded.
echo Building Debug x64 configuration...
echo.

msbuild "Nebula Loader.vcxproj" /p:Configuration=Debug /p:Platform=x64 /v:minimal /nologo

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    pause
    exit /b 1
) else (
    echo.
    echo BUILD SUCCESSFUL!
    if exist "Build\Debug\Nebula Loader.exe" (
        echo Output: Build\Debug\Nebula Loader.exe
    )
)

pause

﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0x320913E1
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0x320913E1
  
  Build signature update completed.
  pch.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\pch.h(60,10): error C1083: Cannot open include file: 'imgui/imgui.h': No such file or directory
  (compiling source file 'src/pch.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\pch.h(60,10): fatal  error C1083: Cannot open include file: 'imgui/imgui.h': No such file or directory

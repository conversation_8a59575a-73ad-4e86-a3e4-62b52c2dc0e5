﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0xAA623D06
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0xAA623D06
  
  Build signature update completed.
  security.cpp
LINK : warning LNK4098: defaultlib 'LIBCMT' conflicts with use of other libs; use /NODEFAULTLIB:library
  Generating code
  5 of 4065 functions ( 0.1%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    31 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
auth.lib(core.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(core.obj)' or at ''; linking object as if no debug info
auth.lib(open.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(open.obj)' or at ''; linking object as if no debug info
auth.lib(codecs.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(codecs.obj)' or at ''; linking object as if no debug info
auth.lib(randombytes.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(randombytes.obj)' or at ''; linking object as if no debug info
auth.lib(runtime.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(runtime.obj)' or at ''; linking object as if no debug info
auth.lib(utils.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(utils.obj)' or at ''; linking object as if no debug info
auth.lib(generichash_blake2b.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(generichash_blake2b.obj)' or at ''; linking object as if no debug info
auth.lib(onetimeauth_poly1305.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(onetimeauth_poly1305.obj)' or at ''; linking object as if no debug info
auth.lib(argon2-core.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(argon2-core.obj)' or at ''; linking object as if no debug info
auth.lib(scalarmult_curve25519.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(scalarmult_curve25519.obj)' or at ''; linking object as if no debug info
auth.lib(stream_chacha20.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(stream_chacha20.obj)' or at ''; linking object as if no debug info
auth.lib(stream_salsa20.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(stream_salsa20.obj)' or at ''; linking object as if no debug info
auth.lib(aead_aegis128l.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(aead_aegis128l.obj)' or at ''; linking object as if no debug info
auth.lib(aead_aegis256.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(aead_aegis256.obj)' or at ''; linking object as if no debug info
auth.lib(hash_sha512_cp.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(hash_sha512_cp.obj)' or at ''; linking object as if no debug info
auth.lib(verify.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(verify.obj)' or at ''; linking object as if no debug info
auth.lib(sign.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(sign.obj)' or at ''; linking object as if no debug info
auth.lib(ed25519_ref10.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(ed25519_ref10.obj)' or at ''; linking object as if no debug info
auth.lib(randombytes_sysrandom.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(randombytes_sysrandom.obj)' or at ''; linking object as if no debug info
auth.lib(blake2b-ref.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(blake2b-ref.obj)' or at ''; linking object as if no debug info
auth.lib(poly1305_donna.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(poly1305_donna.obj)' or at ''; linking object as if no debug info
auth.lib(argon2-fill-block-ref.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(argon2-fill-block-ref.obj)' or at ''; linking object as if no debug info
auth.lib(argon2-fill-block-avx512f.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(argon2-fill-block-avx512f.obj)' or at ''; linking object as if no debug info
auth.lib(argon2-fill-block-avx2.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(argon2-fill-block-avx2.obj)' or at ''; linking object as if no debug info
auth.lib(argon2-fill-block-ssse3.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(argon2-fill-block-ssse3.obj)' or at ''; linking object as if no debug info
auth.lib(blake2b-long.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(blake2b-long.obj)' or at ''; linking object as if no debug info
auth.lib(x25519_ref10.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(x25519_ref10.obj)' or at ''; linking object as if no debug info
auth.lib(chacha20_ref.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(chacha20_ref.obj)' or at ''; linking object as if no debug info
auth.lib(chacha20_dolbeau-avx2.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(chacha20_dolbeau-avx2.obj)' or at ''; linking object as if no debug info
auth.lib(chacha20_dolbeau-ssse3.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(chacha20_dolbeau-ssse3.obj)' or at ''; linking object as if no debug info
auth.lib(salsa20_ref.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(salsa20_ref.obj)' or at ''; linking object as if no debug info
auth.lib(salsa20_xmm6int-sse2.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(salsa20_xmm6int-sse2.obj)' or at ''; linking object as if no debug info
auth.lib(salsa20_xmm6int-avx2.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(salsa20_xmm6int-avx2.obj)' or at ''; linking object as if no debug info
auth.lib(aegis128l_soft.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(aegis128l_soft.obj)' or at ''; linking object as if no debug info
auth.lib(aegis128l_aesni.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(aegis128l_aesni.obj)' or at ''; linking object as if no debug info
auth.lib(aegis256_soft.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(aegis256_soft.obj)' or at ''; linking object as if no debug info
auth.lib(aegis256_aesni.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(aegis256_aesni.obj)' or at ''; linking object as if no debug info
auth.lib(blake2b-compress-ref.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(blake2b-compress-ref.obj)' or at ''; linking object as if no debug info
auth.lib(blake2b-compress-ssse3.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(blake2b-compress-ssse3.obj)' or at ''; linking object as if no debug info
auth.lib(blake2b-compress-sse41.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(blake2b-compress-sse41.obj)' or at ''; linking object as if no debug info
auth.lib(blake2b-compress-avx2.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(blake2b-compress-avx2.obj)' or at ''; linking object as if no debug info
auth.lib(core_salsa_ref.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(core_salsa_ref.obj)' or at ''; linking object as if no debug info
auth.lib(softaes.obj) : warning LNK4099: PDB '' was not found with 'auth.lib(softaes.obj)' or at ''; linking object as if no debug info
freetype.lib(ftinit.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftinit.obj)' or at ''; linking object as if no debug info
freetype.lib(ftbase.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftbase.obj)' or at ''; linking object as if no debug info
freetype.lib(ftsynth.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftsynth.obj)' or at ''; linking object as if no debug info
freetype.lib(autofit.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(autofit.obj)' or at ''; linking object as if no debug info
freetype.lib(truetype.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(truetype.obj)' or at ''; linking object as if no debug info
freetype.lib(type1.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(type1.obj)' or at ''; linking object as if no debug info
freetype.lib(cff.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(cff.obj)' or at ''; linking object as if no debug info
freetype.lib(type1cid.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(type1cid.obj)' or at ''; linking object as if no debug info
freetype.lib(pfr.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(pfr.obj)' or at ''; linking object as if no debug info
freetype.lib(type42.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(type42.obj)' or at ''; linking object as if no debug info
freetype.lib(winfnt.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(winfnt.obj)' or at ''; linking object as if no debug info
freetype.lib(pcf.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(pcf.obj)' or at ''; linking object as if no debug info
freetype.lib(bdf.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(bdf.obj)' or at ''; linking object as if no debug info
freetype.lib(psaux.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(psaux.obj)' or at ''; linking object as if no debug info
freetype.lib(psmodule.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(psmodule.obj)' or at ''; linking object as if no debug info
freetype.lib(pshinter.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(pshinter.obj)' or at ''; linking object as if no debug info
freetype.lib(sfnt.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(sfnt.obj)' or at ''; linking object as if no debug info
freetype.lib(smooth.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(smooth.obj)' or at ''; linking object as if no debug info
freetype.lib(raster.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(raster.obj)' or at ''; linking object as if no debug info
freetype.lib(sdf.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(sdf.obj)' or at ''; linking object as if no debug info
freetype.lib(svg.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(svg.obj)' or at ''; linking object as if no debug info
freetype.lib(ftsystem.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftsystem.obj)' or at ''; linking object as if no debug info
freetype.lib(ftbitmap.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftbitmap.obj)' or at ''; linking object as if no debug info
freetype.lib(ftmm.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftmm.obj)' or at ''; linking object as if no debug info
freetype.lib(ftgzip.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftgzip.obj)' or at ''; linking object as if no debug info
freetype.lib(ftlzw.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftlzw.obj)' or at ''; linking object as if no debug info
freetype.lib(ftdebug.obj) : warning LNK4099: PDB '' was not found with 'freetype.lib(ftdebug.obj)' or at ''; linking object as if no debug info
  Nebula Loader.vcxproj -> C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\Build\DEV\Nebula Loader.exe

{"version": 1, "revision": 0, "rules": [{"primary-output": "C:\\Users\\<USER>\\Desktop\\coding\\nebula\\Nebula loader\\Build\\DEV\\obj\\microsoft\\STL\\std.obj", "outputs": ["std.i", "C:\\Users\\<USER>\\Desktop\\coding\\nebula\\Nebula loader\\Build\\DEV\\obj\\microsoft\\STL\\std.ixx.ifc.dt.module.json", "C:\\Users\\<USER>\\Desktop\\coding\\nebula\\Nebula loader\\Build\\DEV\\obj\\microsoft\\STL\\std.ixx.ifc.dt.d.json", "C:\\Users\\<USER>\\Desktop\\coding\\nebula\\Nebula loader\\Build\\DEV\\obj\\microsoft\\STL\\std.ifc"], "provides": [{"logical-name": "std", "source-path": "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.43.34808\\modules\\std.ixx", "is-interface": true}], "requires": []}]}
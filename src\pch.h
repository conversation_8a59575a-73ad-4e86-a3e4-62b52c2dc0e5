#pragma once

// =============================================================================
// PRECOMPILED HEADER - Core system includes
// =============================================================================

// Windows API Headers
#include <Windows.h>
#include <TlHelp32.h>
#include <dwmapi.h>
#include <winternl.h>
#include <tchar.h>
#include <mmsystem.h>
#include <shlobj.h>

// DirectX Headers
#include <d3d11.h>
#include <dxgi.h>

// Math Headers
#include <corecrt_math_defines.h>
#include <cmath>

// C++ Standard Library - Core
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <stdexcept>
#include <cstdint>
#include <cstdio>
#include <cstring>

// C++ Standard Library - Containers
#include <unordered_map>

// C++ Standard Library - Threading
#include <mutex>
#include <atomic>
#include <thread>
#include <chrono>

// C++ Standard Library - I/O
#include <fstream>
#include <sstream>
#include <filesystem>

// C++ Standard Library - Algorithms
#include <algorithm>

// C++ Standard Library - Modern Features
#include <print>
#include <format>

// =============================================================================
// EXTERNAL LIBRARIES
// =============================================================================

// ImGui - Immediate Mode GUI Library
#include "imgui/imgui.h"
#include "imgui/imgui_impl_dx9.h"
#include "imgui/imgui_impl_win32.h"
#include "imgui/imgui_internal.h"